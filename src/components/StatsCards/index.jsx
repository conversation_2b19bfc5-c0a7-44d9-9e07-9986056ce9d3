"use client";

import { motion } from 'framer-motion';
import { useTranslation } from '@/hooks/useTranslation';
import GSAPTextReveal from '@/components/GSAPTextReveal';
import { getPreset } from '@/components/GSAPTextReveal/presets';
import styles from './style.module.scss';
import Separator from '@/components/Separator';
import AnimatedLink from '@/components/AnimatedLink';


export default function StatsCards() {
  const { t } = useTranslation('agency');

  // Fonction pour construire le label avec AnimatedLink pour les clients satisfaits
  const buildSatisfiedClientsLabel = () => {
    const labelTemplate = t('stats.satisfied_clients');
    const linkText = t('stats.satisfied_clients_link_text');

    if (labelTemplate.includes('{{link}}')) {
      return (
        <>
          {labelTemplate.split('{{link}}')[0]}
          <AnimatedLink href="https://g.page/r/CdYePvCaFA87EAE/review" external>
            {linkText}
          </AnimatedLink>
          {labelTemplate.split('{{link}}')[1]}
        </>
      );
    }

    return labelTemplate;
  };

  const statsData = [
    {
      number: t('stats.projects_number'),
      label: t('stats.projects_completed'),
      color: '#19271B',
      id: 'projects'
    },
    {
      number: t('stats.rating'),
      label: buildSatisfiedClientsLabel(),
      color: '#FF413D',
      id: 'clients'
    },
    {
      number: t('stats.countries_number'),
      label: t('stats.multiple_countries'),
      color: '#C8CEC9',
      id: 'countries'
    }
  ];

  return (
    <section className={styles.statsSection}>
      <div className="container">
        <div className={styles.statsGrid}>
          {statsData.map((stat, index) => (
            <div key={stat.id} className={styles.statCard}>
              <GSAPTextReveal
                as="p"
                className="text-big"
                {...getPreset('lines', { delay: 2 + (index * 0.15), stagger: 0.1 })}
              >
                {stat.label}
              </GSAPTextReveal>
              <Separator
                animated={true}
                className={styles.separator}
              />
              <div
                className={`${styles.maskWrapper} ${styles.statCardBg}`}
                style={{
                  backgroundColor: stat.color,
                  clipPath: "polygon(18% 0%, 100% 0%, 100% 92%, 100% 100%, 0% 100%, 0% 23%)"
                }}
              >
                <div className={styles.cardContent}>
                  <motion.div
                    className={styles.number}
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    viewport={{ once: true, margin: "-50px" }}
                    transition={{ duration: 0.8, ease: 'easeInOut', delay: (index + 0.3) * 0.15 }}
                  >
                    {stat.number}
                  </motion.div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
