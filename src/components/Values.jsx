"use client";

import Description from '@/components/Description';
import Separator from '@/components/Separator';
import { useTranslation } from "@/hooks/useTranslation";

export default function Values() {
  const { t } = useTranslation('agency');

  return (
    <>
      <div className="container">
        <Separator
          animated={true}
          color="#000"
          thickness={1}
          animationDelay={0.1}
        />
      </div>

      <Description
        descriptionTitle={t('values1.title')}
        descriptionText={t('values1.text')}
        showButton={false}
        titleTag="h3"
      />

      <div className="container">
        <Separator
          animated={true}
          color="#000"
          thickness={1}
          animationDelay={0.2}
        />
      </div>

      <Description
        descriptionTitle={t('values2.title')}
        descriptionText={t('values2.text')}
        showButton={false}
        titleTag="h3"
      />

      <div className="container">
        <Separator
          animated={true}
          color="#000"
          thickness={1}
          animationDelay={0.3}
        />
      </div>

      <Description
        descriptionTitle={t('values3.title')}
        descriptionText={t('values3.text')}
        showButton={false}
        titleTag="h3"
      />
    </>
  );
}