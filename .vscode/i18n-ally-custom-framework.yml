# Configuration personnalisée pour i18n Ally
# Adapté pour le système de traduction custom de Kapreon

# Détection des clés de traduction
languageIds:
  - javascript
  - typescript
  - javascriptreact
  - typescriptreact

# Patterns pour détecter les appels de traduction
usageMatchRegex:
  - "\\bt\\s*\\(\\s*['\"`]({key})['\"`]"
  - "getTranslation\\([^)]*\\)\\s*\\(\\s*['\"`]({key})['\"`]"

# Patterns pour extraire les clés
refactorTemplates:
  - source: "t\\('([^']*)'\\)"
    target: "t('$1')"
  - source: 't\("([^"]*)"\)'
    target: "t('$1')"

# Monopoly pour les hooks de traduction
monopoly:
  - "useTranslation"
  - "getTranslation"
