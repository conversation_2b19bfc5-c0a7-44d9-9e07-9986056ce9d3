{"id": "kapreon-custom", "display": "Kapreon Custom Framework", "languageIds": ["javascript", "typescript", "javascriptreact", "typescriptreact"], "usageMatchRegex": ["\\bt\\s*\\(\\s*['\"`]({key})['\"`]", "getTranslation\\([^)]*\\)\\s*\\(\\s*['\"`]({key})['\"`]"], "refactorTemplates": [{"source": "t\\('([^']*)'\\)", "target": "t('$1')"}, {"source": "t\\(\"([^\"]*)\"\\)", "target": "t('$1')"}], "monopoly": ["useTranslation", "getTranslation"], "detection": {"featureFlag": "t(", "packageJSON": ["next", "react"]}}