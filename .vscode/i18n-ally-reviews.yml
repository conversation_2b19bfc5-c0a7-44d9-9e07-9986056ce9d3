# Configuration des règles de révision pour i18n Ally

rules:
  # Vérifier que toutes les clés ont des traductions
  missing-translation:
    enabled: true
    severity: error

  # Vérifier les clés inutilisées
  unused-key:
    enabled: true
    severity: warning

  # Vérifier les clés dupliquées
  duplicate-key:
    enabled: true
    severity: error

  # Vérifier la cohérence des variables
  inconsistent-variables:
    enabled: true
    severity: warning

# Langues à vérifier
locales:
  - fr
  - en

# Ignorer certains patterns
ignore:
  - "*.test.*"
  - "*.spec.*"
  - "node_modules/**"
