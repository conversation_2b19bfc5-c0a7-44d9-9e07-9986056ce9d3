{"i18n-ally.localesPaths": ["locales"], "i18n-ally.keystyle": "nested", "i18n-ally.pathMatcher": "{locale}/**/{namespaces}.json", "i18n-ally.namespace": true, "i18n-ally.defaultNamespace": "common", "i18n-ally.enabledParsers": ["json"], "i18n-ally.sourceLanguage": "fr", "i18n-ally.displayLanguage": "fr", "i18n-ally.enabledFrameworks": ["react"], "i18n-ally.extract.keygenStyle": "camelCase", "i18n-ally.extract.keyMaxLength": 50, "i18n-ally.usage.scanningIgnore": ["node_modules/**", ".next/**", "dist/**", "build/**", "*.config.js", "*.config.ts"], "i18n-ally.annotations": true, "i18n-ally.review.enabled": true, "i18n-ally.review.gutters": true, "i18n-ally.editor.preferEditor": true}